<script setup lang="jsx">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useForm, usePage, useFilter } from '@/hooks'
import { useServerStore } from '@/stores/server'

const serverStore = useServerStore()

const router = useRouter()

const tableData = ref([])

const columns = ref([
  {
    title: '序号',
    colKey: 'serial-number',
    width: 60,
  },
  {
    title: '表名',
    colKey: 'nameOriginal',
    ellipsis: true,
  },
  {
    title: '描述',
    colKey: 'comment',
    ellipsis: true,
    cell: (h, { row }) => <span>{row.comment || '-'}</span>,
  },
  {
    title: '数据源',
    colKey: 'dataSourceCode',
    ellipsis: true,
  },
  {
    title: '最近更新时间',
    colKey: 'updatedDateTime',
    ellipsis: true,
  },
  {
    title: '操作',
    col<PERSON>ey: 'operation',
    cell: (h, { row }) => (
      <t-space>
        <t-link theme="primary" hover="color" onClick={() => handleTrace(row)}>
          血缘
        </t-link>
        <t-link theme="primary" hover="color" onClick={() => handleUpdate(row)}>
          更新
        </t-link>
      </t-space>
    ),
  },
])

function handleTrace(row) {
  console.log(row)
  router.push({ name: 'tableTrace' })
}

function handleUpdate(row) {
  console.log(row)
}

async function fetchData() {
  const params = {
    query: {
      nameOriginal: form.value.nameOriginal || null,
      dataSourceCode: form.value.dataSourceCode || null,
    },
    page: {
      number: pagination.current,
      size: pagination.pageSize,
    },
  }
  const { body } = await serverStore.fetchTableList(params)
  tableData.value = body?.items || []
  setPageTotal(body?.page?.totalCount || 0)
}

const { form, resetForm } = useForm({
  nameOriginal: '',
  dataSourceCode: '',
})

const { pagination, setPageCurrent, setPageTotal } = usePage(fetchData)

const { onSearch, onReset } = useFilter({
  resetForm,
  setPageCurrent,
  fetchData,
})

const dataSourceOptions = ref([])
async function getDataSourceOptions() {
  const params = {
    query: {},
    page: {
      number: 1,
      size: 9999,
    },
  }
  const { body } = await serverStore.fetchDataSourceList(params)
  dataSourceOptions.value =
    body?.items.map((item) => ({
      label: item.code,
      value: item.code,
    })) || []
}

onMounted(() => {
  fetchData()
  getDataSourceOptions()
})
</script>

<template>
  <div class="trace-lineage-table-page">
    <div class="serach-wrap">
      <t-form :data="form" layout="inline" labelAlign="left" labelWidth="10">
        <t-form-item label="表名">
          <t-input v-model="form.nameOriginal" clearable @change="onSearch"></t-input>
        </t-form-item>
        <t-form-item label="数据源">
          <t-select
            v-model="form.dataSourceCode"
            :options="dataSourceOptions"
            filterable
            clearable
            @change="onSearch"
          ></t-select>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button variant="outline" @click="onSearch">查询</t-button>
            <t-button variant="outline" @click="onReset">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>

    <t-table
      row-key="id"
      :data="tableData"
      :columns="columns"
      :pagination="pagination"
      max-height="100%"
    ></t-table>
  </div>
</template>

<style lang="less" scoped>
.trace-lineage-table-page {
  height: 100%;
  .serach-wrap {
    margin-bottom: var(--trace-lineage-margin);
    :deep(.t-form__item) {
      &:not(.t-form__item-with-extra) {
        margin-bottom: 0;
      }
    }
  }
  :deep(.t-table) {
    height: calc(100% - 32px - var(--trace-lineage-margin));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
