import { defineStore } from 'pinia'
import axios from '@/server/client'
import {
  API_TABLE_LIST,
  API_DATASOURCE_LIST,
  API_FIELD_LIST,
  API_INTERFACE_LIST,
  API_LINEAGE_FIELD,
} from '@/server/api'

export const useServerStore = defineStore('server', {
  state: () => ({}),
  actions: {
    // 查询表血缘列表
    fetchTableList(data) {
      return axios({
        url: API_TABLE_LIST,
        method: 'post',
        data,
      })
    },
    // 查询数据源的选项
    fetchDataSourceList(data, options = { loading: false }) {
      return axios({
        url: API_DATASOURCE_LIST,
        method: 'post',
        data,
        ...options,
      })
    },
    // 查询字段血缘列表
    fetchFieldList(data) {
      return axios({
        url: API_FIELD_LIST,
        method: 'post',
        data,
      })
    },
    // 查询接口血缘列表
    fetchInterfaceList(data) {
      return axios({
        url: API_INTERFACE_LIST,
        method: 'post',
        data,
      })
    },
    // 查询字段血缘的详情
    fetchLineageField(data) {
      return axios({
        url: API_LINEAGE_FIELD,
        method: 'post',
        data,
      })
    },
  },
  getters: {},
})
