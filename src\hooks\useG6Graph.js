import { ref, onMounted, onBeforeUnmount } from 'vue'
import { Graph } from '@antv/g6'
import { registerJobNode } from '@/utils/graph'

// 注册自定义节点
registerJobNode()

export function useG6Graph(payload, options = {}) {
  const paintboardRef = ref(null)
  const graph = ref(null)
  let isInitializing = false

  // 默认配置
  const defaultOptions = {
    padding: 50,
    autoResize: true,
    autoFit: {
      type: 'view',
    },
    background: '#eeefef',
    node: { ...options.node },
    edge: {
      type: 'cubic-horizontal',
      animation: {
        enter: false,
      },
    },
    layout: {
      type: 'dendrogram',
      direction: 'LR', // H / V / LR / RL / TB / BT
      nodeSep: 80, // 40 + 40
      rankSep: 300, // 240 + 60
    },
    behaviors: [
      'drag-canvas',
      'zoom-canvas',
      {
        type: 'drag-element',
        enable: true,
        // 添加配置来避免事件冲突
        eventName: 'drag',
      },
      // 'collapse-expand',
      'hover-activate', // 节点hover
      'click-select', // 节点click
    ],
    ...options,
  }

  const initGraph = async () => {
    // 防止重复初始化
    if (isInitializing) return Promise.resolve()
    if (graph.value) return Promise.resolve() // 如果已经初始化，直接返回

    isInitializing = true
    try {
      destroyGraph()

      // 确保容器已经准备好
      if (!paintboardRef.value) {
        throw new Error('Container not ready')
      }

      const temGraph = new Graph({
        container: paintboardRef.value,
        ...defaultOptions,
        data: payload,
      })

      // 异步渲染
      await temGraph.render()

      graph.value = temGraph
      console.log('Graph initialized successfully')
      return Promise.resolve()
    } catch (error) {
      console.error('Failed to initialize G6 graph:', error)
      return Promise.reject(error)
    } finally {
      isInitializing = false
    }
  }

  const destroyGraph = () => {
    if (graph.value) {
      graph.value.destroy()
      graph.value = null
    }
  }

  const updateGraphData = (newData) => {
    if (graph.value && newData) {
      try {
        graph.value.changeData(newData)
        console.log('Graph data updated successfully')
      } catch (error) {
        console.error('Failed to update graph data:', error)
      }
    } else {
      console.warn('Graph not initialized or no data provided')
    }
  }

  // 自动在组件挂载和卸载时调用
  onMounted(() => {
    initGraph()
  })

  onBeforeUnmount(() => {
    destroyGraph()
  })

  return {
    paintboardRef,
    graph, // 可选：暴露 graph 实例用于外部操作
    initGraph,
    destroyGraph,
    updateGraphData,
  }
}
