/**
 * @description 该方法用于开发环境某些接口的重定向。如果出现跨域，建议使用跨域浏览器
 * <AUTHOR>
 */
const isDevelopment = import.meta.env.MODE === 'development'
let proxy = {}
if (isDevelopment) {
  // 使用动态导入代替require
  import('./config.js')
    .then((module) => {
      proxy = module.default || module
    })
    .catch((error) => {
      console.warn('Failed to load proxy config:', error)
    })
}

function pathRewrite(rules = {}, url) {
  let rewriteUrl = url
  Object.keys(rules).forEach((rule) => {
    const reg = new RegExp(rule)
    const val = Reflect.get(rules, rule)
    rewriteUrl = rewriteUrl.replace(reg, val)
  })
  return rewriteUrl
}

export default function (url) {
  let urlFix = url.indexOf('/') !== 0 && url.indexOf('http') !== 0 ? `/${url}` : url
  let base = ''
  const rules = Object.keys(proxy)
  for (let i = 0; i < rules.length; i++) {
    const match = rules[i]
    if (url.indexOf(match) === 0) {
      base = proxy[match].target
      urlFix = pathRewrite(proxy[match].pathRewrite, urlFix)
      break
    }
  }
  return `${base}${urlFix}`
}
