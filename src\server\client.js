import axios from 'axios'
import { host } from './constant'
import proxy<PERSON><PERSON> from './proxy'
import { MessagePlugin, LoadingPlugin } from 'tdesign-vue-next'

const instance = axios.create({
  baseURL: host,
  method: 'post',
  timeout: 30000,
})

const { CancelToken } = axios
let reqNum = 0
let loadingInstance = null // 用于保存 LoadingPlugin 返回的实例

function throwReject(reject, data, config, message) {
  const { showErrorMsg = true } = config
  if (config.throwError || config.throwError === undefined) {
    showErrorMsg && MessagePlugin.error(message)
  }
  reject(data)
}

function ajax(options) {
  const cloneConfig = { ...options }

  if (cloneConfig.url) {
    cloneConfig.url = proxyApi(cloneConfig.url)
  }

  if (cloneConfig.cancel !== undefined) {
    const fn = cloneConfig.cancel
    cloneConfig.cancelToken = new CancelToken((source) => fn(source))
  }

  let { file } = cloneConfig
  if (file) {
    const fileType = Object.prototype.toString.call(file)
    if (fileType === '[object Blob]' && !(fileType === '[object File]')) {
      file = new File([file], file.name || 'file', { type: file.type })
    }

    const formData = new FormData()
    if (cloneConfig.data) {
      Object.keys(cloneConfig.data).forEach((key) => {
        formData.append(key, cloneConfig.data[key])
      })
    }
    formData.append('file', file, file.name)

    const headers = cloneConfig.headers || {}
    cloneConfig.headers = {
      'Content-Type': 'multipart/form-data',
      ...headers,
    }
    cloneConfig.data = formData
  }

  if (cloneConfig.method && cloneConfig.method.toLowerCase() === 'get') {
    cloneConfig.params = {
      ...(cloneConfig.params || {}),
      timestamp: Date.now(),
    }
  }

  return new Promise((resolve, reject) => {
    if (cloneConfig.loading !== false) {
      reqNum++
    }

    if (!loadingInstance && reqNum > 0) {
      loadingInstance = LoadingPlugin({
        fullscreen: true,
        content: '加载中...',
      })
    }

    instance(cloneConfig)
      .then((res) => {
        if (cloneConfig.customResolve) {
          resolve(res)
        } else {
          const { data } = res
          if (data.success === true) {
            resolve(data)
          } else {
            let msg = `${res.status} ${res.statusText}`
            if (typeof data === 'object') {
              msg = data.message || data.msg || '系统错误'
            }
            throwReject(reject, data, cloneConfig, msg)
          }
        }
      })
      .catch((reason) => {
        if (axios.isCancel(reason)) {
          reject(reason)
          return
        }

        let message = reason.message
        if (reason.response?.data?.message) {
          message = reason.response.data.message
        }

        throwReject(reject, reason, cloneConfig, message)
      })
      .finally(() => {
        if (cloneConfig.loading !== false) {
          reqNum--
        }

        if (reqNum <= 0 && loadingInstance) {
          loadingInstance.hide()
          loadingInstance = null
        }
      })
  })
}

export default ajax
