/**
 * @param {function} options.cancel 取消ajax，默认无
 * @param {boolean} options.loading 是否自动添加loading，默认为true
 * @param {boolean} options.throwError 是否自动抛出错误，默认为true
 * @param {blob|formdata} options.file 文件
 * @param {blob|formdata} options.customResolve 自定义axios then
 * <AUTHOR>
 */

import axios from 'axios';
import Vue from 'vue';
import { host } from './constant';
import proxyApi from './proxy';

const instance = axios.create({
  baseURL: host,
});
instance.defaults.baseURL = host;
instance.defaults.method = 'post';
// instance.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
instance.defaults.timeout = 30000;

const { CancelToken } = axios;
let reqNum = 0;
let loading = null;

function throwReject(reject, data, config, message) {
  const { showErrorMsg = true } = config;
  if (config.throwError || config.throwError === undefined) {
    showErrorMsg && Vue.prototype.$message.error(message);
  }
  reject(data);
}

function ajax(options) {
  const cloneCofig = { ...options };
  if (cloneCofig.url) {
    cloneCofig.url = proxyApi(cloneCofig.url);
  }

  if (cloneCofig.cancel !== undefined) {
    const fn = cloneCofig.cancel;
    cloneCofig.cancelToken = new CancelToken(source => fn(source));
  }
  let { file } = cloneCofig;
  if (file) {
    const fileType = Object.prototype.toString.call(cloneCofig.file);
    if (fileType === '[object File]' || fileType === '[object Blob]') {
      if (fileType === '[object Blob]') {
        file = new File([file], file.name, {
          type: file.type,
        });
      }
    }
    const formData = new FormData();
    if (cloneCofig.data) {
      Object.keys(cloneCofig.data)
        .forEach((key) => {
          formData.append(key, cloneCofig.data[key]);
        });
    }
    formData.append('file', file, file.name);
    const headers = cloneCofig.header || {};
    cloneCofig.header = {
      'Content-Type': 'multipart/form-data',
      ...headers,
    };
    cloneCofig.data = formData;
  }
  if (cloneCofig.method && cloneCofig.method.toLocaleUpperCase() === 'GET') {
    // get请求自动添加时间戳，防止缓存
    cloneCofig.params = {
      ...(cloneCofig.params || {}),
      timestamp: new Date().getTime(),
    };
  }

  const promise = new Promise((resolve, reject) => {
    const { throwError, cancel, ...axiosConfig } = cloneCofig;
    if (cloneCofig.loading !== false) {
      reqNum += 1;
    }
    if (loading === null && reqNum) {
      loading = Vue.prototype.$loading({
        fullscreen: true,
      });
    }
    instance(axiosConfig)
      .then((res) => {
        if (cloneCofig.customResolve) {
          resolve(res);
        } else {
          const { data } = res;
          if (data.success === true) {
            resolve(data);
          } else {
            let msg = `${res.status} ${res.statusText}`;
            if (typeof data === 'object') {
              msg = data.message || data.msg || '系统错误';
            }
            throwReject(reject, data, cloneCofig, msg);
          }
        }
      })
      .catch((reason) => {
        let { message } = reason;
        const { code } = reason;
        if (code === 'ERR_CANCELED') {
          reject(reason);
          return;
        }
        if (reason.response && reason.response.data && reason.response.data.message) {
          message = reason.response.data.message;
        }
        throwReject(reject, reason, cloneCofig, message);
      })
      .finally(() => {
        if (cloneCofig.loading !== false) {
          reqNum -= 1;
        }
        if (reqNum === 0 && loading) {
          loading.hide();
          loading = null;
        }
      });
  });

  return promise;
}

export default ajax;
