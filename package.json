{"name": "trace-lineage-web", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@antv/g6": "^5.0.49", "@tencent/hr-vue-next": "^0.0.1-beta.4", "@vueuse/core": "^14.0.0-alpha.0", "axios": "^1.11.0", "echarts": "^6.0.0", "g6-extension-vue": "^0.1.0", "mitt": "^3.0.1", "pinia": "^3.0.3", "tdesign-vue-next": "^1.16.0", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "less": "^4.4.1", "prettier": "3.6.2", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}