import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), vueJsx(), vueDevTools()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: 'local.woa.com',
    port: 9081, // 指定端口
    proxy: {
      '/api': {
        target: 'http://************:8051',
        changeOrigin: true,
        pathRewrite: {
          '/api': '/manage/api',
        },
        headers: {
          staffid: '00316174',
          staffname: 'nebulaliao',
        },
      },
    },
  },
})
