<script setup>
import { computed } from 'vue'
import emitter, { EVENT_NAMES } from '@/utils/emitter'
import {
  DataBaseIcon,
  TableIcon,
  ControlPlatformIcon,
  LogoWindowsIcon,
} from 'tdesign-icons-vue-next'
// 按实际情况调整
// DataBaseIcon 数据源 数据模型 上游表 参照目标表 数据表
// TableIcon 参照字段 字段
// ControlPlatformIcon 下游接口 接口 参照接口
// LogoWindowsIcon 应用

const props = defineProps({
  data: Object,
})

const nodeType = computed(() => props.data.data.type)

const nodeTitle = computed(() => {
  switch (nodeType.value) {
    case 'datasource':
      return '数据源'
    case 'table':
      return '数据表'
    case 'field':
      return '字段'
    case 'interface':
      return '接口'
    case 'application':
      return '应用'
    default:
      return '未知'
  }
})

const componentName = computed(() => {
  switch (nodeType.value) {
    case 'datasource':
      return DataBaseIcon
    case 'table':
      return TableIcon
    case 'field':
      return TableIcon
    case 'interface':
      return ControlPlatformIcon
    case 'application':
      return LogoWindowsIcon
    default:
      return DataBaseIcon
  }
})

// hover-activate
const isActive = computed(() => props.data.states?.includes('active'))
// click-select
const isSelected = computed(() => props.data.states?.includes('selected'))
// hover-activate & click-select
const isActived = computed(() => isActive.value || isSelected.value)

function handleNodeClick() {
  emitter.emit(EVENT_NAMES.NODE_CLICK, props.data)
}
</script>

<template>
  <div class="job-node" :class="{ active: isActived }" @dblclick="handleNodeClick">
    <span class="job-node-title">{{ nodeTitle }}</span>
    <component :is="componentName" class="job-node-icon" :class="{ 'icon-active': isActived }" />
    <span class="job-node-label">{{ props.data.id }}</span>
  </div>
</template>

<style lang="less" scoped>
.job-node {
  width: 240px;
  height: 40px;
  color: #333;
  background: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #ccc;

  position: relative;
  .job-node-title {
    position: absolute;
    top: 0;
    left: 0;
    transform: translate(0, -100%);
    color: #969696;
  }
  .job-node-icon {
    color: var(--trace-lineage-color-theme);
    margin-right: 5px;
    flex-shrink: 0; /* 防止图标被压缩 */
    width: 14px; /* 固定图标宽度 */
    height: 14px; /* 固定图标高度 */
  }
  .job-node-label {
    flex: 1;
    min-width: 0; /* 允许文本收缩 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
    white-space: nowrap; /* 不换行 */
  }
}
.active {
  color: #fff;
  background: #6696f6;
}
.icon-active {
  color: #fff !important;
}
</style>
