<script setup>
import { ref } from 'vue'
import { use<PERSON>oute, useRouter } from 'vue-router'
import { DataBaseIcon, TableIcon, ControlPlatformIcon } from 'tdesign-icons-vue-next'
import CommonBreadcrumb from '@/components/CommonBreadcrumb.vue'

const route = useRoute()
const router = useRouter()
const routeName = route.path.split('/')[3]

const actived = ref(routeName)
function handleTabChange(tab) {
  router.push({ name: tab })
}
</script>

<template>
  <div class="trace-lineage-page">
    <CommonBreadcrumb :breadcrumbs="['数据血缘']"></CommonBreadcrumb>
    <div class="trace-lineage-page__content">
      <t-tabs v-model="actived" @change="handleTabChange">
        <t-tab-panel value="table">
          <template #label> <DataBaseIcon style="margin-right: 4px" /> 表血缘 </template>
        </t-tab-panel>
        <t-tab-panel value="field">
          <template #label> <TableIcon style="margin-right: 4px" /> 字段血缘 </template>
        </t-tab-panel>
        <t-tab-panel value="interface">
          <template #label> <ControlPlatformIcon style="margin-right: 4px" /> 接口血缘 </template>
        </t-tab-panel>
      </t-tabs>
      <div class="trace-lineage-page__main">
        <router-view v-slot="{ Component }">
          <keep-alive>
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.trace-lineage-page {
  height: 100%;
  .trace-lineage-page__content {
    margin: var(--trace-lineage-margin);
    // 42px 为 breadcrumb 高度 + 上下margin
    height: calc(100% - 42px - var(--trace-lineage-margin) * 2);
    background-color: #fff;
  }
  .trace-lineage-page__main {
    padding: var(--trace-lineage-padding);
    height: calc(100% - 48px);
  }
}
</style>
