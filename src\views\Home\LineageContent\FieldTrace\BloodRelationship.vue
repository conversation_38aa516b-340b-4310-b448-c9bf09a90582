<script setup lang="jsx">
import { onMounted, ref } from 'vue'
import { RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { useG6Graph } from '@/hooks'
import { treeToGraphData } from '@antv/g6'
import JobNode from '@/components/JobNode.vue'
import { useServerStore } from '@/stores/server'
import { useRoute } from 'vue-router'

const serverStore = useServerStore()

const route = useRoute()

const treeData = ref({})
const graphData = ref(null)

// 先创建 useG6Graph 实例，但不传入初始数据
const { paintboardRef, initGraph } = useG6Graph(
  {},
  {
    node: {
      type: 'job-node',
      style: {
        component: (data) => <JobNode data={Object.assign({}, data)} />,
      },
    },
  },
)

async function initData() {
  const params = {
    // dataSourceCode: route.query.dataSourceCode,
    // tableName: route.query.tableName,
    // columnName: route.query.columnName,
    dataSourceCode: 'hrdw-dashboard-new',
    tableName: 'fdw_ads_insight_od_manager_info',
    columnName: 'org_id',
  }
  const { body } = await serverStore.fetchLineageField(params)
  treeData.value = {
    id: route.query.id,
    data: {
      type: 'field',
      label: route.query.columnName,
    },
    children: body.interfaceMainInfos?.map((item) => ({
      id: item.id,
      data: {
        type: 'interface',
        label: item.code,
      },
    })),
  }
  graphData.value = treeToGraphData(treeData.value)

  // 数据获取完成后，用数据初始化图形
  await initGraphWithData()
}

async function initGraphWithData() {
  if (graphData.value) {
    await initGraph(graphData.value)
  }
}

onMounted(() => {
  initData()
})
</script>

<template>
  <div class="blood-relationship-page">
    <div class="paintboard-tool-left">
      <t-space size="10px">
        <t-input placeholder="数据表">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-input placeholder="接口">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
      </t-space>
    </div>
    <div class="paintboard-tool-right">
      <span class="paintboard-tool-right-wrap">
        <RefreshIcon />
      </span>
    </div>
    <div ref="paintboardRef" class="paintboard-inner"></div>
  </div>
</template>

<style lang="less" scoped>
.blood-relationship-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  background-color: #eee;
  position: relative;
  .paintboard-tool-left,
  .paintboard-tool-right {
    position: absolute;
    top: 10px;
    height: 32px;
    line-height: 32px;
    z-index: 1001;
  }
  .paintboard-tool-left {
    left: 10px;
  }
  .paintboard-tool-right {
    right: 10px;
    .paintboard-tool-right-wrap {
      height: 32px;
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      background-color: #fff;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  .paintboard-inner {
    height: 100%;
    width: 100%;
  }
}
</style>
